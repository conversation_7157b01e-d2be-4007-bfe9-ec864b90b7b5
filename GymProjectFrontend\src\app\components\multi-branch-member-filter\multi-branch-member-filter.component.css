/* Modern Card Styles */
.modern-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.modern-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.modern-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-card-body {
  padding: 1.5rem;
}

.modern-card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

/* Filter Mode Toggle */
.filter-mode-toggle .btn {
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* Statistics Cards */
.stats-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.5rem;
  color: white;
}

.stats-primary .stats-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-success .stats-icon {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stats-info .stats-icon {
  background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
}

.stats-warning .stats-icon {
  background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
}

.stats-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stats-content p {
  margin: 0;
  color: #7f8c8d;
  font-weight: 500;
}

/* Form Styles */
.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.modern-table th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  font-weight: 600;
  padding: 1rem;
  border-bottom: 2px solid #dee2e6;
  text-align: left;
}

.modern-table td {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.member-row {
  transition: all 0.3s ease;
}

.member-row:hover {
  background-color: #f8f9fa;
}

/* Member Info Styles */
.member-info {
  display: flex;
  align-items: center;
}

.modern-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.member-details strong {
  color: #2c3e50;
  font-size: 1rem;
}

/* Branch Summary */
.branch-summary .badge {
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Branch Details */
.branch-details-row {
  background-color: #f8f9fa;
}

.branch-details-container {
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.branch-detail-item {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.branch-detail-item:last-child {
  margin-bottom: 0;
}

.branch-header {
  cursor: pointer;
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #495057;
  transition: all 0.3s ease;
}

.branch-header:hover {
  color: #667eea;
}

.package-details {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #dee2e6;
}

.package-item {
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.package-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.package-item:last-child {
  margin-bottom: 0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.action-buttons .btn {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
}

/* Status Colors */
.text-success {
  color: #28a745 !important;
  font-weight: 600;
}

.text-warning {
  color: #ffc107 !important;
  font-weight: 600;
}

.text-danger {
  color: #dc3545 !important;
  font-weight: 600;
}

/* Badges */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
}

.bg-info {
  background-color: #17a2b8 !important;
}

.bg-secondary {
  background-color: #6c757d !important;
}

/* Pagination */
.pagination .page-link {
  border-radius: 6px;
  margin: 0 0.125rem;
  border: 1px solid #dee2e6;
  color: #495057;
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  background-color: #667eea;
  border-color: #667eea;
  color: white;
}

.pagination .page-item.active .page-link {
  background-color: #667eea;
  border-color: #667eea;
}

/* Loading Spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-card-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .filter-mode-toggle {
    width: 100%;
  }
  
  .filter-mode-toggle .btn {
    width: 48%;
  }
  
  .stats-card {
    text-align: center;
  }
  
  .stats-icon {
    margin: 0 auto 1rem auto;
  }
  
  .member-info {
    flex-direction: column;
    text-align: center;
  }
  
  .modern-avatar {
    margin: 0 0 0.5rem 0;
  }
  
  .action-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .branch-details-container {
    padding: 0.5rem;
  }
  
  .package-item .row {
    text-align: center;
  }
  
  .package-item .col-md-4,
  .package-item .col-md-3,
  .package-item .col-md-2 {
    margin-bottom: 0.5rem;
  }
}

/* Animation Classes */
.zoom-in {
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
