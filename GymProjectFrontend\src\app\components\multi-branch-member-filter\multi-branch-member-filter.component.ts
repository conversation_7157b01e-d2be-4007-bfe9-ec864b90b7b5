import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { MembershipTypeService } from '../../services/membership-type.service';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { MultiBranchMemberFilterDto, BranchMembershipSummary, MembershipPackageDetail } from '../../models/multi-branch-member-filter';
import { MembershipType } from '../../models/membershipType';
import { faEdit, faSnowflake, faTrashAlt, faEye, faChevronDown, faChevronRight } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-multi-branch-member-filter',
  templateUrl: './multi-branch-member-filter.component.html',
  styleUrls: ['./multi-branch-member-filter.component.css'],
  standalone: false
})
export class MultiBranchMemberFilterComponent implements OnInit, OnDestroy {
  members: MultiBranchMemberFilterDto[] = [];
  memberFilterText: string = '';
  private searchTextSubject = new Subject<string>();
  genderFilter: string = '';
  branchFilter: string = '';
  membershipTypes: MembershipType[] = [];
  
  // Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faSnowflake = faSnowflake;
  faEye = faEye;
  faChevronDown = faChevronDown;
  faChevronRight = faChevronRight;
  
  // Loading and pagination
  isLoading: boolean = false;
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  totalActiveMembers: number = 0;
  
  // Expanded states for two-level filtering
  expandedMembers: Set<number> = new Set();
  expandedBranches: Map<string, Set<number>> = new Map();
  selectedPackageDetails: Map<string, MembershipPackageDetail[]> = new Map();
  
  // Filter mode: 'branch' for level 1, 'package' for level 2
  filterMode: 'branch' | 'package' = 'branch';
  selectedBranch: string = '';
  
  // Statistics
  genderCounts = {
    all: 0,
    male: 0,
    female: 0
  };
  branchCounts: { [key: string]: number } = {};

  constructor(
    private memberService: MemberService,
    private membershipTypeService: MembershipTypeService,
    private dialog: MatDialog,
    private toastrService: ToastrService
  ) {
    this.searchTextSubject.pipe(
      debounceTime(750),
      distinctUntilChanged()
    ).subscribe(searchText => {
      this.memberFilterText = searchText;
      this.currentPage = 1;
      this.loadMembers();
    });
  }

  ngOnInit(): void {
    this.getBranches();
    this.loadMembers();
    this.getTotalActiveMembers();
  }

  ngOnDestroy(): void {
    this.searchTextSubject.complete();
  }

  searchTextChanged(text: string) {
    this.searchTextSubject.next(text);
  }

  onFilterChange(): void {
    this.currentPage = 1;
    this.loadMembers();
    this.getTotalActiveMembers();
  }

  // Switch between branch view (level 1) and package view (level 2)
  switchFilterMode(mode: 'branch' | 'package', branch?: string): void {
    this.filterMode = mode;
    if (mode === 'package' && branch) {
      this.selectedBranch = branch;
      this.branchFilter = branch;
    } else if (mode === 'branch') {
      this.selectedBranch = '';
      this.branchFilter = '';
    }
    this.onFilterChange();
  }

  // Toggle member expansion (show/hide branches)
  toggleMemberExpansion(memberId: number): void {
    if (this.expandedMembers.has(memberId)) {
      this.expandedMembers.delete(memberId);
    } else {
      this.expandedMembers.add(memberId);
    }
  }

  // Toggle branch expansion (show/hide package details)
  toggleBranchExpansion(memberId: number, branch: string): void {
    const key = `${memberId}-${branch}`;
    if (!this.expandedBranches.has(key)) {
      this.expandedBranches.set(key, new Set());
    }
    
    const branchSet = this.expandedBranches.get(key)!;
    if (branchSet.has(memberId)) {
      branchSet.delete(memberId);
    } else {
      branchSet.add(memberId);
      // Load package details for this branch
      this.loadPackageDetails(memberId, branch);
    }
  }

  // Load package details for a specific member and branch
  loadPackageDetails(memberId: number, branch: string): void {
    const key = `${memberId}-${branch}`;
    if (this.selectedPackageDetails.has(key)) {
      return; // Already loaded
    }

    this.memberService.getMembershipPackageDetailsByBranch(memberId, branch).subscribe({
      next: (response) => {
        if (response.success) {
          this.selectedPackageDetails.set(key, response.data);
        }
      },
      error: (error) => {
        console.error('Error loading package details:', error);
        this.toastrService.error('Paket detayları yüklenirken hata oluştu');
      }
    });
  }

  // Check if member is expanded
  isMemberExpanded(memberId: number): boolean {
    return this.expandedMembers.has(memberId);
  }

  // Check if branch is expanded
  isBranchExpanded(memberId: number, branch: string): boolean {
    const key = `${memberId}-${branch}`;
    const branchSet = this.expandedBranches.get(key);
    return branchSet ? branchSet.has(memberId) : false;
  }

  // Get package details for a member and branch
  getPackageDetails(memberId: number, branch: string): MembershipPackageDetail[] {
    const key = `${memberId}-${branch}`;
    return this.selectedPackageDetails.get(key) || [];
  }

  loadMembers() {
    this.isLoading = true;
    const gender = this.genderFilter ? parseInt(this.genderFilter) : undefined;

    this.memberService
      .getMultiBranchMemberDetailsPaginated(
        this.currentPage,
        this.memberFilterText,
        gender,
        this.branchFilter
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.members = response.data.data;
            this.totalPages = response.data.totalPages;
            this.totalItems = response.data.totalCount;
            this.calculateFilterCounts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching members:', error);
          this.toastrService.error(
            'Üyeler yüklenirken bir hata oluştu.',
            'Hata'
          );
          this.isLoading = false;
        },
      });
  }

  getTotalActiveMembers() {
    this.memberService.getTotalActiveMembers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalActiveMembers = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching total members:', error);
      },
    });
  }

  calculateFilterCounts() {
    this.genderCounts.all = this.totalItems;
  
    this.memberService.getActiveMemberCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.genderCounts.male = response.data['male'];
          this.genderCounts.female = response.data['female'];
        }
      },
      error: (error) => {
        console.error('Error fetching gender counts:', error);
      }
    });
  
    this.memberService.getBranchCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.branchCounts = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching branch counts:', error);
      }
    });
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadMembers();
    }
  }

  getBranches() {
    this.membershipTypeService.getMembershipTypes().subscribe((response) => {
      this.membershipTypes = this.getUniqueBranches(response.data);
    });
  }

  getUniqueBranches(membershipTypes: MembershipType[]): MembershipType[] {
    const uniqueBranches: MembershipType[] = [];
    const branchMap = new Map<string, boolean>();

    membershipTypes.forEach((type) => {
      if (!branchMap.has(type.branch)) {
        branchMap.set(type.branch, true);
        uniqueBranches.push(type);
      }
    });

    return uniqueBranches;
  }

  // Get total remaining days for display
  getTotalRemainingDays(member: MultiBranchMemberFilterDto): number {
    return member.totalRemainingDays;
  }

  // Get branch count for a member
  getBranchCount(member: MultiBranchMemberFilterDto): number {
    return member.branchMemberships.length;
  }

  // Format remaining days display
  formatRemainingDays(days: number): string {
    if (days <= 0) return 'Süresi dolmuş';
    if (days === 1) return '1 gün';
    return `${days} gün`;
  }

  // Get CSS class for remaining days
  getRemainingDaysClass(days: number): string {
    if (days <= 0) return 'text-danger';
    if (days <= 3) return 'text-danger';
    if (days <= 10) return 'text-warning';
    return 'text-success';
  }
}
