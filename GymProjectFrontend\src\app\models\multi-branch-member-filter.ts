export interface MultiBranchMemberFilterDto {
  memberID: number;
  name: string;
  phoneNumber: string;
  gender: number;
  isActive: boolean;
  branchMemberships: BranchMembershipSummary[];
  totalRemainingDays: number;
  earliestStartDate?: Date;
  latestEndDate?: Date;
}

export interface BranchMembershipSummary {
  branch: string;
  totalRemainingDays: number;
  startDate?: Date;
  endDate?: Date;
  hasActiveMembership: boolean;
  hasFutureMembership: boolean;
  packageDetails: MembershipPackageDetail[];
}

export interface MembershipPackageDetail {
  membershipID: number;
  membershipTypeID: number;
  typeName: string;
  branch: string;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  isActive: boolean;
  isFrozen: boolean;
  creationDate?: Date;
  updatedDate?: Date;
  lastPaymentAmount?: number;
  lastPaymentMethod?: string;
}

export interface MemberMultipleMembershipsDto {
  memberID: number;
  memberName: string;
  phoneNumber: string;
  gender: number;
  activeMemberships: MembershipDetailForDeletion[];
  totalActiveMemberships: number;
  totalRemainingDays: number;
  totalPaidAmount: number;
}

export interface MembershipDetailForDeletion {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  isActive: boolean;
  isFrozen: boolean;
  creationDate?: Date;
  updatedDate?: Date;
  payments: PaymentDetailForDeletion[];
  totalPaidAmount: number;
  totalRemainingDebt: number;
  hasPendingPayments: boolean;
  deletionWarnings: string[];
  canBeDeleted: boolean;
  deletionBlockReason?: string;
}

export interface PaymentDetailForDeletion {
  paymentID: number;
  paymentAmount: number;
  paymentMethod: string;
  paymentStatus: string;
  paymentDate: Date;
  remainingDebtAmount?: number;
  hasRemainingDebt: boolean;
}
