using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// İki seviyeli filtreleme sistemi için DTO
    /// Seviye 1: Branş bazlı toplam görünüm
    /// Seviye 2: Paket detay görünümü
    /// </summary>
    public class MultiBranchMemberFilterDto : IDto
    {
        public int MemberID { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public int Gender { get; set; }
        public bool IsActive { get; set; }
        
        // Branş bazlı üyelik bilgileri
        public List<BranchMembershipSummary> BranchMemberships { get; set; } = new List<BranchMembershipSummary>();
        
        // Toplam kalan gün (tüm branşlar)
        public int TotalRemainingDays { get; set; }
        
        // En erken başlangıç tarihi
        public DateTime? EarliestStartDate { get; set; }
        
        // En geç bitiş tarihi
        public DateTime? LatestEndDate { get; set; }
    }

    /// <summary>
    /// Branş bazlı üyelik özeti
    /// </summary>
    public class BranchMembershipSummary : IDto
    {
        public string Branch { get; set; }
        public int TotalRemainingDays { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool HasActiveMembership { get; set; }
        public bool HasFutureMembership { get; set; }
        
        // Bu branştaki paket detayları
        public List<MembershipPackageDetail> PackageDetails { get; set; } = new List<MembershipPackageDetail>();
    }

    /// <summary>
    /// Paket detay bilgileri (Seviye 2 filtreleme için)
    /// </summary>
    public class MembershipPackageDetail : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string TypeName { get; set; }
        public string Branch { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsActive { get; set; }
        public bool IsFrozen { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public decimal? LastPaymentAmount { get; set; }
        public string LastPaymentMethod { get; set; }
    }
}
