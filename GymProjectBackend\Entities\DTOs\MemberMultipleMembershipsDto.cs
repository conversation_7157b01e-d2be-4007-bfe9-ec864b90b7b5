using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Üyenin tüm aktif üyeliklerini listeleyen DTO (Akıllı silme sistemi için)
    /// </summary>
    public class MemberMultipleMembershipsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public int Gender { get; set; }
        
        // Üyenin tüm aktif üyelikleri
        public List<MembershipDetailForDeletion> ActiveMemberships { get; set; } = new List<MembershipDetailForDeletion>();
        
        // Toplam aktif üyelik sayısı
        public int TotalActiveMemberships { get; set; }
        
        // Toplam kalan gün
        public int TotalRemainingDays { get; set; }
        
        // Toplam ödenen tutar
        public decimal TotalPaidAmount { get; set; }
    }

    /// <summary>
    /// Silme işlemi için üyelik detayları
    /// </summary>
    public class MembershipDetailForDeletion : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsActive { get; set; }
        public bool IsFrozen { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        
        // Ödeme bilgileri
        public List<PaymentDetailForDeletion> Payments { get; set; } = new List<PaymentDetailForDeletion>();
        public decimal TotalPaidAmount { get; set; }
        public decimal TotalRemainingDebt { get; set; }
        public bool HasPendingPayments { get; set; }
        
        // Uyarı mesajları
        public List<string> DeletionWarnings { get; set; } = new List<string>();
        public bool CanBeDeleted { get; set; } = true;
        public string DeletionBlockReason { get; set; }
    }

    /// <summary>
    /// Silme işlemi için ödeme detayları
    /// </summary>
    public class PaymentDetailForDeletion : IDto
    {
        public int PaymentID { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public string PaymentStatus { get; set; }
        public DateTime PaymentDate { get; set; }
        public decimal? RemainingDebtAmount { get; set; }
        public bool HasRemainingDebt { get; set; }
    }
}
