<div class="container-fluid">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card">
        <div class="modern-card-header">
          <h4 class="mb-0">
            <i class="fas fa-users me-2"></i>
            Çok Branşlı Üye Yönetimi
          </h4>
          <div class="filter-mode-toggle">
            <button 
              class="btn btn-sm me-2"
              [class.btn-primary]="filterMode === 'branch'"
              [class.btn-outline-primary]="filterMode !== 'branch'"
              (click)="switchFilterMode('branch')">
              <i class="fas fa-layer-group me-1"></i>
              Branş Görünümü
            </button>
            <button 
              class="btn btn-sm"
              [class.btn-primary]="filterMode === 'package'"
              [class.btn-outline-primary]="filterMode !== 'package'"
              [disabled]="!selectedBranch"
              (click)="switchFilterMode('package', selectedBranch)">
              <i class="fas fa-list-ul me-1"></i>
              Paket Detayları
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="stats-card stats-primary">
        <div class="stats-icon">
          <i class="fas fa-users"></i>
        </div>
        <div class="stats-content">
          <h3>{{ totalActiveMembers }}</h3>
          <p>Toplam Aktif Üye</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="stats-card stats-success">
        <div class="stats-icon">
          <i class="fas fa-male"></i>
        </div>
        <div class="stats-content">
          <h3>{{ genderCounts.male }}</h3>
          <p>Erkek Üye</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="stats-card stats-info">
        <div class="stats-icon">
          <i class="fas fa-female"></i>
        </div>
        <div class="stats-content">
          <h3>{{ genderCounts.female }}</h3>
          <p>Kadın Üye</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="stats-card stats-warning">
        <div class="stats-icon">
          <i class="fas fa-dumbbell"></i>
        </div>
        <div class="stats-content">
          <h3>{{ getBranchCount({ branchMemberships: Object.keys(branchCounts) } as any) }}</h3>
          <p>Aktif Branş</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card">
        <div class="modern-card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-search me-2"></i>
                  Üye Ara
                </label>
                <input
                  type="text"
                  class="form-control"
                  placeholder="Ad, soyad veya telefon ile ara..."
                  (input)="searchTextChanged($event.target.value)"
                />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-venus-mars me-2"></i>
                  Cinsiyet
                </label>
                <select
                  class="form-select"
                  [(ngModel)]="genderFilter"
                  (change)="onFilterChange()"
                >
                  <option value="">Tümü</option>
                  <option value="1">Erkek ({{ genderCounts.male }})</option>
                  <option value="2">Kadın ({{ genderCounts.female }})</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-dumbbell me-2"></i>
                  Branş
                </label>
                <select
                  class="form-select"
                  [(ngModel)]="branchFilter"
                  (change)="onFilterChange()"
                >
                  <option value="">Tüm Branşlar</option>
                  <option *ngFor="let type of membershipTypes" [value]="type.branch">
                    {{ type.branch }} ({{ branchCounts[type.branch] || 0 }})
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                  <button
                    class="btn btn-outline-secondary"
                    (click)="genderFilter = ''; branchFilter = ''; onFilterChange()"
                  >
                    <i class="fas fa-times me-1"></i>
                    Temizle
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Members Table -->
  <div class="row">
    <div class="col-12">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            {{ filterMode === 'branch' ? 'Branş Bazlı Görünüm' : 'Paket Detay Görünümü' }}
            <span class="badge bg-primary ms-2">{{ totalItems }} üye</span>
          </h5>
        </div>
        
        <div class="modern-card-body">
          <div class="table-container" *ngIf="!isLoading">
            <table class="modern-table">
              <thead>
                <tr>
                  <th style="width: 40px;"></th>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Üye Bilgileri
                  </th>
                  <th>
                    <i class="fas fa-dumbbell me-2"></i>
                    Branş/Paket Bilgileri
                  </th>
                  <th>
                    <i class="fas fa-calendar-day me-2"></i>
                    Kalan Gün
                  </th>
                  <th>
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let member of members" class="member-row">
                  <td>
                    <button
                      class="btn btn-sm btn-outline-secondary"
                      (click)="toggleMemberExpansion(member.memberID)"
                      [title]="isMemberExpanded(member.memberID) ? 'Daralt' : 'Genişlet'"
                    >
                      <fa-icon 
                        [icon]="isMemberExpanded(member.memberID) ? faChevronDown : faChevronRight">
                      </fa-icon>
                    </button>
                  </td>
                  <td>
                    <div class="member-info">
                      <div class="modern-avatar" 
                           [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
                        {{ member.name.charAt(0) }}
                      </div>
                      <div class="member-details">
                        <strong>{{ member.name }}</strong>
                        <small class="text-muted d-block">{{ member.phoneNumber }}</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="branch-summary">
                      <span class="badge bg-info me-1" *ngFor="let branch of member.branchMemberships">
                        {{ branch.branch }}
                      </span>
                      <small class="text-muted d-block">
                        {{ getBranchCount(member) }} branş
                      </small>
                    </div>
                  </td>
                  <td>
                    <span [class]="getRemainingDaysClass(member.totalRemainingDays)">
                      <strong>{{ formatRemainingDays(member.totalRemainingDays) }}</strong>
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button
                        class="btn btn-sm btn-outline-primary me-1"
                        title="Detayları Görüntüle"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </button>
                      <button
                        class="btn btn-sm btn-outline-warning me-1"
                        title="Üyelik Güncelle"
                      >
                        <fa-icon [icon]="faEdit"></fa-icon>
                      </button>
                      <button
                        class="btn btn-sm btn-outline-info me-1"
                        title="Dondur"
                      >
                        <fa-icon [icon]="faSnowflake"></fa-icon>
                      </button>
                      <button
                        class="btn btn-sm btn-outline-danger"
                        title="Sil"
                      >
                        <fa-icon [icon]="faTrashAlt"></fa-icon>
                      </button>
                    </div>
                  </td>
                </tr>
                
                <!-- Expanded Branch Details -->
                <tr *ngIf="isMemberExpanded(member.memberID)" class="branch-details-row">
                  <td></td>
                  <td colspan="4">
                    <div class="branch-details-container">
                      <div *ngFor="let branch of member.branchMemberships" class="branch-detail-item">
                        <div class="branch-header" 
                             (click)="toggleBranchExpansion(member.memberID, branch.branch)">
                          <fa-icon 
                            [icon]="isBranchExpanded(member.memberID, branch.branch) ? faChevronDown : faChevronRight"
                            class="me-2">
                          </fa-icon>
                          <strong>{{ branch.branch }}</strong>
                          <span class="badge bg-secondary ms-2">
                            {{ formatRemainingDays(branch.totalRemainingDays) }}
                          </span>
                          <span class="badge bg-info ms-1">
                            {{ branch.packageDetails.length }} paket
                          </span>
                        </div>
                        
                        <!-- Package Details -->
                        <div *ngIf="isBranchExpanded(member.memberID, branch.branch)" 
                             class="package-details mt-2">
                          <div *ngFor="let package of getPackageDetails(member.memberID, branch.branch)" 
                               class="package-item">
                            <div class="row align-items-center">
                              <div class="col-md-4">
                                <strong>{{ package.typeName }}</strong>
                                <small class="text-muted d-block">
                                  {{ package.startDate | date:'dd.MM.yyyy' }} - 
                                  {{ package.endDate | date:'dd.MM.yyyy' }}
                                </small>
                              </div>
                              <div class="col-md-3">
                                <span [class]="getRemainingDaysClass(package.remainingDays)">
                                  {{ formatRemainingDays(package.remainingDays) }}
                                </span>
                              </div>
                              <div class="col-md-3">
                                <span *ngIf="package.lastPaymentAmount" class="text-success">
                                  {{ package.lastPaymentAmount | currency:'TRY':'symbol':'1.0-0' }}
                                </span>
                                <small class="text-muted d-block" *ngIf="package.lastPaymentMethod">
                                  {{ package.lastPaymentMethod }}
                                </small>
                              </div>
                              <div class="col-md-2">
                                <span class="badge" 
                                      [class.bg-success]="package.isActive && !package.isFrozen"
                                      [class.bg-warning]="package.isFrozen"
                                      [class.bg-secondary]="!package.isActive">
                                  {{ package.isFrozen ? 'Dondurulmuş' : (package.isActive ? 'Aktif' : 'Pasif') }}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Yükleniyor...</span>
            </div>
          </div>

          <!-- Empty State -->
          <div *ngIf="!isLoading && members.length === 0" class="text-center py-4">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Üye bulunamadı</h5>
            <p class="text-muted">Arama kriterlerinizi değiştirip tekrar deneyin.</p>
          </div>
        </div>

        <!-- Pagination -->
        <div class="modern-card-footer" *ngIf="totalPages > 1">
          <nav aria-label="Sayfa navigasyonu">
            <ul class="pagination justify-content-center mb-0">
              <li class="page-item" [class.disabled]="currentPage === 1">
                <a class="page-link" (click)="onPageChange(currentPage - 1)">
                  <i class="fas fa-chevron-left"></i>
                </a>
              </li>
              <li class="page-item" 
                  *ngFor="let page of [].constructor(totalPages); let i = index"
                  [class.active]="currentPage === i + 1">
                <a class="page-link" (click)="onPageChange(i + 1)">{{ i + 1 }}</a>
              </li>
              <li class="page-item" [class.disabled]="currentPage === totalPages">
                <a class="page-link" (click)="onPageChange(currentPage + 1)">
                  <i class="fas fa-chevron-right"></i>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
